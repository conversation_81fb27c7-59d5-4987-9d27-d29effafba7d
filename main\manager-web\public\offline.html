<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline Mode - <PERSON><PERSON><PERSON> Console</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      background-color: #f5f7fa;
      color: #333;
    }
    .container {
      text-align: center;
      padding: 2rem;
      background: white;
      border-radius: 10px;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
      max-width: 80%;
    }
    h1 {
      margin-bottom: 1rem;
      color: #409EFF;
    }
    p {
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }
    .btn {
      background-color: #409EFF;
      color: white;
      border: none;
      padding: 0.8rem 1.5rem;
      border-radius: 4px;
      font-size: 1rem;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    .btn:hover {
      background-color: #337ecc;
    }
    .icon {
      font-size: 4rem;
      margin-bottom: 1rem;
      color: #409EFF;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>You are currently offline</h1>
    <p>It looks like there's a problem with your network connection. Unable to connect to XiaoZhi console server.</p>
    <p>Some cached content and static resources may still be available, but functionality may be limited.</p>
    <button class="btn" onclick="window.location.reload()">Try to Reconnect</button>
  </div>

  <script>
    // Detect network status changes
    window.addEventListener('online', () => {
      window.location.reload();
    });
  </script>
</body>
</html> 